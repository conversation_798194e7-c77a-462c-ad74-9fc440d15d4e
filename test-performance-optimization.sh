#!/bin/bash

# 性能优化测试脚本
# 测试系统性能监控和优化功能

echo "🚀 MusicDou Performance Optimization Test"
echo "=========================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi

# 检查服务器是否运行
echo "🔍 Checking if server is running..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Server is running"
else
    echo "❌ Server is not running. Please start the server first:"
    echo "   npm run dev"
    exit 1
fi

# 检查Docker服务是否运行
echo "🔍 Checking Docker services..."
if docker ps | grep -q "musicdou-mongodb"; then
    echo "✅ MongoDB container is running"
else
    echo "❌ MongoDB container is not running"
    echo "   Please start Docker services: npm run docker:start"
    exit 1
fi

if docker ps | grep -q "musicdou-redis"; then
    echo "✅ Redis container is running"
else
    echo "❌ Redis container is not running"
    echo "   Please start Docker services: npm run docker:start"
    exit 1
fi

# 运行性能优化测试
echo ""
echo "🧪 Running performance optimization tests..."
echo "============================================"

node test-performance-optimization.js

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Performance optimization tests completed successfully!"
    echo ""
    echo "📊 Next steps:"
    echo "1. Check the performance dashboard at: http://localhost:3000/api/v1/performance/overview"
    echo "2. Monitor system health at: http://localhost:3000/api/v1/performance/health"
    echo "3. Review optimization suggestions at: http://localhost:3000/api/v1/performance/suggestions"
    echo ""
    echo "🔧 Performance monitoring features:"
    echo "- Real-time system metrics"
    echo "- Request performance tracking"
    echo "- Cache hit rate monitoring"
    echo "- Database query optimization"
    echo "- Memory usage monitoring"
    echo "- Slow query detection"
    echo "- Automatic performance suggestions"
else
    echo ""
    echo "❌ Some performance tests failed"
    echo "Please check the error messages above and fix any issues"
    exit 1
fi
